@model mvcproject.Models.TextData

@{
    ViewData["Title"] = "Text Data Details";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">@ViewData["Title"]</h4>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-3">@Html.DisplayNameFor(model => model.Id)</dt>
                        <dd class="col-sm-9">@Html.DisplayFor(model => model.Id)</dd>

                        <dt class="col-sm-3">@Html.DisplayNameFor(model => model.Content)</dt>
                        <dd class="col-sm-9">
                            <div class="border p-3 bg-light rounded">
                                @Html.DisplayFor(model => model.Content)
                            </div>
                        </dd>

                        <dt class="col-sm-3">@Html.DisplayNameFor(model => model.CreatedAt)</dt>
                        <dd class="col-sm-9">@Model.CreatedAt.ToString("MMMM dd, yyyy 'at' HH:mm")</dd>

                        <dt class="col-sm-3">@Html.DisplayNameFor(model => model.UpdatedAt)</dt>
                        <dd class="col-sm-9">
                            @if (Model.UpdatedAt.HasValue)
                            {
                                @Model.UpdatedAt.Value.ToString("MMMM dd, yyyy 'at' HH:mm")
                            }
                            else
                            {
                                <span class="text-muted">Never updated</span>
                            }
                        </dd>
                    </dl>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
