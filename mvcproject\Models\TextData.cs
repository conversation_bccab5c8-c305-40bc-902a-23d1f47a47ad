using System.ComponentModel.DataAnnotations;

namespace mvcproject.Models
{
    public class TextData
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(500, ErrorMessage = "Content cannot exceed 500 characters.")]
        [Display(Name = "Text Content")]
        public string Content { get; set; } = string.Empty;
        
        [Display(Name = "Created At")]
        public DateTime CreatedAt { get; set; }
        
        [Display(Name = "Updated At")]
        public DateTime? UpdatedAt { get; set; }
    }
}
