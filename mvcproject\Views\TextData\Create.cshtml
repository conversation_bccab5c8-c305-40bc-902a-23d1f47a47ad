@model mvcproject.Models.TextData

@{
    ViewData["Title"] = "Create Text Data";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">@ViewData["Title"]</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Create">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Content" class="form-label"></label>
                            <textarea asp-for="Content" class="form-control" rows="5" placeholder="Enter your text here..."></textarea>
                            <span asp-validation-for="Content" class="text-danger"></span>
                            <div class="form-text">Maximum 500 characters allowed.</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
