{"GlobalPropertiesHash": "UYo1wlr4DLlnnulWhHP8/JmkQgSaEhMKp4BOr+lcb0E=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["2RVg3B6BDR/IGkH05TgWcxjTqsg79fanKv9ANtRVmMY=", "MU7art6/Ne0C99oya+88u23CYtR+mZFI60ul8Klo4xA=", "jJn/nOaMHWASnAXym7FB864tzwAkdn3y9wkzzhIN7BA=", "X8PI6dNtoAwnlfn/xWemp4Xw/GgZ3eCz/NaafkwTkFA=", "0ZkxcZmb7rVCyKBqLJUu1v+PcduHVw4D7acdzFWypgw=", "VC32Rl3e+YvNwLSPZYprG3kn/ht6fQKaMcAxjM8LqOE=", "ehYkI67aD3Blj7oRtHT9M43RPFczdpo9owq8nTAoDHU=", "nVQPmuViJ8LYyuwG/g0wQKp1kCjhWZix+PTN5RPb9A8=", "emC47RvS/nyHMUoFrqdQLXQOp+fRvRN/V8EoIJAEaqc=", "anw5TZPAty4LWwBZBcvndlsqDziXdNN1AvxjJTPx8W4=", "6zGxDOxyzY0Cz+SEAuFA7F0J4I6nqdTyZbGTfazgVzM=", "xbXste6ZBCtI9fykZP/IeYc/JV0wqSZxk7pA295RUBw=", "6Z/SZuR2kj8IT+U9WhApooe9wb/l72X4ocqIgbaKX8U=", "IMKbmneOFh5dty0LLp3pq2H8Nbc+djB4bzaeLKT2fVc=", "kwQauanWQs/jgUAwybfaV6jA1bvEG647CQ6hCNZruxI=", "1W360BipiYWodglEuKXFob1YIksse1PpjP76pLnhnvg=", "kN5shPhkhDlefV4bO0YsmVMcow06ywtT0oY4jE0ukuA=", "Nk/YDvndW+xGYlLQ4r9ApqETmsw9b4VVUKFhWtIS87w=", "wnSSM0nJliLUWGyot9b12If3xLTMpIBYxOUTucIrozg=", "pGB4qoi8ejqjC7siH9OS07+MY65KPy7DglakWyQhOxU=", "BX01txKMRZ5cFHzJma3a8IC3ZviePUC/fIo3i5rHXO8=", "MO9y0xskNF5gAIHti+ZfiXFNIRnRFQDQPmFDs1oMgUo=", "QGPnRySs2QC/QJdA6RihBVDZThYrXCZXVus2xKNAd78=", "3RTdRav7TCEcwFeVAAgeqK1YMqbrOgr4bEqvDqe8qb4=", "1HR1HqvFmJ8lZX6iFZf6jvapiOWumzyJJIv/iBs6jRg=", "YfiYLt7+Q3GmQUwnBnPbhLZVwretSagu+Xyaeq28y5U=", "kDbZlqA58PWBd058N27wr110HLKMIQMWXrmLqqM1qS0=", "AX1VF5tnsvl4tuuSPzuTzgwtg9H7rulMhfjnJLTYRmQ=", "ZrBH4brHnGothTsTKer1cIw/CxbgDVJH1kegm5vIu68=", "1756cExW3XKfY9ZI9nC6k2WwbFgDKYWTviq5YCi5AHc=", "1/6K2/McyoXbVeHXhoUBu8tf5db6E0PcNSQsDeeXG4o=", "2qCe3az4bs/4VBs1qwa2rjU2czbbfm+BgsXyyamEogU=", "a0M5KhtGnebWaRMghnSTPPbv4FXpLgnrbunSnNwz2sQ=", "k8H42uKpFudOgn76UQsaxvPW3XhPjmCoFdx8bK6fnjQ=", "nE+DxJc6sZspBRF9Oz4SRAZEntWMGEXR69igXIbYtJY=", "hKayHzFIuF6rFLGjHZy7GHnP86FM2SJIShjvuL2VnUc=", "aERGm38o5JqPlrB6Mk1JfmLttIRaBFoAXpA37jg9qs4=", "iv4CoTTc8CCfuVPoRAhxwKdPBLcrC14I2tFFV3+aEwE=", "Wlc8+vS+BGUscIGgUDtLTxGE09CciM8GGw7DZPKrW/w=", "R+15TD+tWghMMZxKlWeZK1GTlfzoMebU16DE3iNrIns=", "348oL9xfif9jW+O1BibxJm1ksql1OuA3FFFGV1GfEs4=", "3n0tz+KaX3GUxe3Pgy6zlLwxxl6ChttMNVwGPdhJZlg=", "I+qaiO2YVHxKXRH2FvHWMAPR41q8CPmN5EgYA1a9gUk=", "pNrbrN7PuJmpnIZr/xsMiX0QpNEER7hpQYWw5VipAVE=", "TRDaUmdNBoMHgpKZ9k/gO+opaC4u3VEYXiz82Z4roHw=", "D9dnD2KpevjuNk4q0SFLcgiHc8U4MVa46jEope/utrg=", "w5uIseDU5+QNQNHF+qWmTieOsXIGI7Vh+kIE92CEEg0=", "JOwODlkaZn1+C1Vb3+D5KhEFQZpfKtnojHILY2x8768=", "AjTa7/DDJejTdm+HdCzbuiBOYPRv4SbtTE98kPjfSoE=", "qsccvTL+U3lfKPyFUn0YoSwKZlGb1kbWz1iQ3qM+ON8=", "X86awfNLDiiCgGCEF7RizrtVlV43bW/ubgxLBgub7oo=", "fWU3bSKkRZZtjARXrceiPqrOV3wD7oKHf1Kx469RA0I=", "mSCfu0TSKBDsbzKlBw7cuI75U/3nG1RbNpve7gV2RME=", "8KO1T/HOY/ZhrPGckNqUCN2DomOWhtwPp1uxcL+c/oI=", "NgjH/E01SI67g+VqV6dm4r1zE8IvTobQ+p+wzXiMP84=", "qsK0UpEa8kZ95wF6PArsmvHqGGcDrpC6rXMPqa/tEjg=", "djBYW/0BBJf/5e9TNgRSNpNYuAastSZfuJTDun1Rxog=", "Z1rKpIoWLbHhB1NpeJkXLqOUzSDX4/NbAIvsShnbTg4=", "5jhbBdqgL4RidawUgY9qMQq+rttlSuruhZViw0iZwK4=", "087cWd5QH7cyCyPE/IEvsXstXd1R22gfrcxsgtjJ26I=", "TqMjVfRIlqRQnLlLMHLrlk4AeV3E0emjOlivbYjV+JM=", "WwbCxpzrrAsrgJTdOTRrSi49WJXrjyNVEepeRbr2OP4=", "9VYiL+QjCUfnXom2GZA5hyLP9Xd0WBWGbGfeJfYpG0w=", "87L0efyYx6SC0VxjDu+JCZfp7Ur0R7gV2QF64tki8/Y=", "iV6sdA4WGlpq/rUxz4lfHhPCz4EQhQ6UcmnDMKsaf6A=", "MuHIDEfDBfJphMuqN2Bc+Er7WGUOg1mI6CZ5a3VjwF8=", "DBD6XLnEQuqE7hr9mqHsdY5wggRVXuXKI87bF13OSu0=", "7MNaEP70TWJaKD0BDuCwwqhreZpvFa1iYnNJ6FakT+k=", "YctoJOm0n93fCpymUQAzadr0MujJhKA36G2C/nCSWYI=", "a1Igsy7KSa0qXfRWxJG1UXvL2j7qv42fl41fe/jCucE=", "yfZE914oqbwG6MRxYWy0+b6V2Qvtuj5qdnVOFgME+CQ=", "x9eDV4hVbnw7glwMu3F5UFk1E74CjeAUQuER6Usiib4=", "uSaACZ2ITV6YxPFowNrJsXdXYTp7o8l5gzwExzVnL6k="], "CachedAssets": {"2RVg3B6BDR/IGkH05TgWcxjTqsg79fanKv9ANtRVmMY=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\css\\site.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-09-13T11:29:13.1620573+00:00"}, "MU7art6/Ne0C99oya+88u23CYtR+mZFI60ul8Klo4xA=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\favicon.ico", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-09-13T11:29:13.118919+00:00"}, "jJn/nOaMHWASnAXym7FB864tzwAkdn3y9wkzzhIN7BA=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\js\\site.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-09-13T11:29:13.1630892+00:00"}, "X8PI6dNtoAwnlfn/xWemp4Xw/GgZ3eCz/NaafkwTkFA=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-09-13T11:29:13.0693593+00:00"}, "0ZkxcZmb7rVCyKBqLJUu1v+PcduHVw4D7acdzFWypgw=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-09-13T11:29:13.0709131+00:00"}, "VC32Rl3e+YvNwLSPZYprG3kn/ht6fQKaMcAxjM8LqOE=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-09-13T11:29:13.0709131+00:00"}, "ehYkI67aD3Blj7oRtHT9M43RPFczdpo9owq8nTAoDHU=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-09-13T11:29:13.0714349+00:00"}, "nVQPmuViJ8LYyuwG/g0wQKp1kCjhWZix+PTN5RPb9A8=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-09-13T11:29:13.0724804+00:00"}, "emC47RvS/nyHMUoFrqdQLXQOp+fRvRN/V8EoIJAEaqc=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-09-13T11:29:13.0735448+00:00"}, "anw5TZPAty4LWwBZBcvndlsqDziXdNN1AvxjJTPx8W4=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-09-13T11:29:13.0746154+00:00"}, "6zGxDOxyzY0Cz+SEAuFA7F0J4I6nqdTyZbGTfazgVzM=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-09-13T11:29:13.0751447+00:00"}, "xbXste6ZBCtI9fykZP/IeYc/JV0wqSZxk7pA295RUBw=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-09-13T11:29:13.0756739+00:00"}, "6Z/SZuR2kj8IT+U9WhApooe9wb/l72X4ocqIgbaKX8U=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-09-13T11:29:13.0762051+00:00"}, "IMKbmneOFh5dty0LLp3pq2H8Nbc+djB4bzaeLKT2fVc=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-09-13T11:29:13.0762051+00:00"}, "kwQauanWQs/jgUAwybfaV6jA1bvEG647CQ6hCNZruxI=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-09-13T11:29:13.0772132+00:00"}, "1W360BipiYWodglEuKXFob1YIksse1PpjP76pLnhnvg=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-09-13T11:29:13.0772132+00:00"}, "kN5shPhkhDlefV4bO0YsmVMcow06ywtT0oY4jE0ukuA=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-09-13T11:29:13.0782135+00:00"}, "Nk/YDvndW+xGYlLQ4r9ApqETmsw9b4VVUKFhWtIS87w=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-09-13T11:29:13.0782135+00:00"}, "wnSSM0nJliLUWGyot9b12If3xLTMpIBYxOUTucIrozg=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-09-13T11:29:13.0790108+00:00"}, "pGB4qoi8ejqjC7siH9OS07+MY65KPy7DglakWyQhOxU=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-09-13T11:29:13.079516+00:00"}, "BX01txKMRZ5cFHzJma3a8IC3ZviePUC/fIo3i5rHXO8=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-09-13T11:29:13.0805791+00:00"}, "MO9y0xskNF5gAIHti+ZfiXFNIRnRFQDQPmFDs1oMgUo=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-09-13T11:29:13.0811073+00:00"}, "QGPnRySs2QC/QJdA6RihBVDZThYrXCZXVus2xKNAd78=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-09-13T11:29:13.0816355+00:00"}, "3RTdRav7TCEcwFeVAAgeqK1YMqbrOgr4bEqvDqe8qb4=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-09-13T11:29:13.0816355+00:00"}, "1HR1HqvFmJ8lZX6iFZf6jvapiOWumzyJJIv/iBs6jRg=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-09-13T11:29:13.0831503+00:00"}, "YfiYLt7+Q3GmQUwnBnPbhLZVwretSagu+Xyaeq28y5U=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-09-13T11:29:13.0841564+00:00"}, "kDbZlqA58PWBd058N27wr110HLKMIQMWXrmLqqM1qS0=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-09-13T11:29:13.0847369+00:00"}, "AX1VF5tnsvl4tuuSPzuTzgwtg9H7rulMhfjnJLTYRmQ=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-09-13T11:29:13.0852658+00:00"}, "ZrBH4brHnGothTsTKer1cIw/CxbgDVJH1kegm5vIu68=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-09-13T11:29:13.0868521+00:00"}, "1756cExW3XKfY9ZI9nC6k2WwbFgDKYWTviq5YCi5AHc=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-09-13T11:29:13.0878602+00:00"}, "1/6K2/McyoXbVeHXhoUBu8tf5db6E0PcNSQsDeeXG4o=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-09-13T11:29:13.0896579+00:00"}, "2qCe3az4bs/4VBs1qwa2rjU2czbbfm+BgsXyyamEogU=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-09-13T11:29:13.0906981+00:00"}, "a0M5KhtGnebWaRMghnSTPPbv4FXpLgnrbunSnNwz2sQ=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-09-13T11:29:13.0923036+00:00"}, "k8H42uKpFudOgn76UQsaxvPW3XhPjmCoFdx8bK6fnjQ=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-09-13T11:29:13.0933461+00:00"}, "nE+DxJc6sZspBRF9Oz4SRAZEntWMGEXR69igXIbYtJY=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-09-13T11:29:13.0949199+00:00"}, "hKayHzFIuF6rFLGjHZy7GHnP86FM2SJIShjvuL2VnUc=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-09-13T11:29:13.0959678+00:00"}, "aERGm38o5JqPlrB6Mk1JfmLttIRaBFoAXpA37jg9qs4=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-09-13T11:29:13.0970235+00:00"}, "iv4CoTTc8CCfuVPoRAhxwKdPBLcrC14I2tFFV3+aEwE=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-09-13T11:29:13.0980682+00:00"}, "Wlc8+vS+BGUscIGgUDtLTxGE09CciM8GGw7DZPKrW/w=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-09-13T11:29:13.0991199+00:00"}, "R+15TD+tWghMMZxKlWeZK1GTlfzoMebU16DE3iNrIns=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-09-13T11:29:13.1001667+00:00"}, "348oL9xfif9jW+O1BibxJm1ksql1OuA3FFFGV1GfEs4=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-09-13T11:29:13.1012123+00:00"}, "3n0tz+KaX3GUxe3Pgy6zlLwxxl6ChttMNVwGPdhJZlg=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-09-13T11:29:13.1017329+00:00"}, "I+qaiO2YVHxKXRH2FvHWMAPR41q8CPmN5EgYA1a9gUk=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-09-13T11:29:13.1027924+00:00"}, "pNrbrN7PuJmpnIZr/xsMiX0QpNEER7hpQYWw5VipAVE=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-09-13T11:29:13.1038389+00:00"}, "TRDaUmdNBoMHgpKZ9k/gO+opaC4u3VEYXiz82Z4roHw=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-09-13T11:29:13.1049182+00:00"}, "D9dnD2KpevjuNk4q0SFLcgiHc8U4MVa46jEope/utrg=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-09-13T11:29:13.1054487+00:00"}, "w5uIseDU5+QNQNHF+qWmTieOsXIGI7Vh+kIE92CEEg0=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-09-13T11:29:13.105981+00:00"}, "JOwODlkaZn1+C1Vb3+D5KhEFQZpfKtnojHILY2x8768=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-09-13T11:29:13.071962+00:00"}, "AjTa7/DDJejTdm+HdCzbuiBOYPRv4SbtTE98kPjfSoE=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-09-13T11:29:13.1225158+00:00"}, "qsccvTL+U3lfKPyFUn0YoSwKZlGb1kbWz1iQ3qM+ON8=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-09-13T11:29:13.1235999+00:00"}, "X86awfNLDiiCgGCEF7RizrtVlV43bW/ubgxLBgub7oo=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-09-13T11:29:13.0751447+00:00"}, "fWU3bSKkRZZtjARXrceiPqrOV3wD7oKHf1Kx469RA0I=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-09-13T11:29:13.1183708+00:00"}, "mSCfu0TSKBDsbzKlBw7cuI75U/3nG1RbNpve7gV2RME=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-09-13T11:29:13.118919+00:00"}, "8KO1T/HOY/ZhrPGckNqUCN2DomOWhtwPp1uxcL+c/oI=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-09-13T11:29:13.1199298+00:00"}, "NgjH/E01SI67g+VqV6dm4r1zE8IvTobQ+p+wzXiMP84=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-09-13T11:29:13.1199298+00:00"}, "qsK0UpEa8kZ95wF6PArsmvHqGGcDrpC6rXMPqa/tEjg=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-09-13T11:29:13.074086+00:00"}, "djBYW/0BBJf/5e9TNgRSNpNYuAastSZfuJTDun1Rxog=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-09-13T11:29:13.1080906+00:00"}, "Z1rKpIoWLbHhB1NpeJkXLqOUzSDX4/NbAIvsShnbTg4=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-09-13T11:29:13.1086147+00:00"}, "5jhbBdqgL4RidawUgY9qMQq+rttlSuruhZViw0iZwK4=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-09-13T11:29:13.1118118+00:00"}, "087cWd5QH7cyCyPE/IEvsXstXd1R22gfrcxsgtjJ26I=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-09-13T11:29:13.1128762+00:00"}, "TqMjVfRIlqRQnLlLMHLrlk4AeV3E0emjOlivbYjV+JM=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-09-13T11:29:13.1139315+00:00"}, "WwbCxpzrrAsrgJTdOTRrSi49WJXrjyNVEepeRbr2OP4=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-09-13T11:29:13.116787+00:00"}, "9VYiL+QjCUfnXom2GZA5hyLP9Xd0WBWGbGfeJfYpG0w=": {"Identity": "D:\\NET\\Projects\\mvcproject\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "mvcproject", "SourceType": "Discovered", "ContentRoot": "D:\\NET\\Projects\\mvcproject\\wwwroot\\", "BasePath": "_content/mvcproject", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-09-13T11:29:13.073014+00:00"}}, "CachedCopyCandidates": {}}