# Test script to verify API functionality
Write-Host "Testing API endpoints..."

# Test GET all text data
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5186/api/textdata" -Method Get
    Write-Host "GET /api/textdata - Success: $($response.Count) items returned"
} catch {
    Write-Host "GET /api/textdata - Error: $($_.Exception.Message)"
}

# Test POST new text data
try {
    $newData = @{
        Content = "Test data from PowerShell script"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5186/api/textdata" -Method Post -Body $newData -ContentType "application/json"
    Write-Host "POST /api/textdata - Success: Created item with ID $($response.Id)"
    $createdId = $response.Id
} catch {
    Write-Host "POST /api/textdata - Error: $($_.Exception.Message)"
}

# Test GET specific item
if ($createdId) {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:5186/api/textdata/$createdId" -Method Get
        Write-Host "GET /api/textdata/$createdId - Success: Retrieved item"
    } catch {
        Write-Host "GET /api/textdata/$createdId - Error: $($_.Exception.Message)"
    }
}

Write-Host "API testing completed."
