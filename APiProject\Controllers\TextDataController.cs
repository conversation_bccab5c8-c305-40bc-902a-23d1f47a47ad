using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using APiProject.Data;
using APiProject.Models;

namespace APiProject.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TextDataController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public TextDataController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/TextData
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TextData>>> GetTextData()
        {
            return await _context.TextData.OrderByDescending(t => t.CreatedAt).ToListAsync();
        }

        // GET: api/TextData/5
        [HttpGet("{id}")]
        public async Task<ActionResult<TextData>> GetTextData(int id)
        {
            var textData = await _context.TextData.FindAsync(id);

            if (textData == null)
            {
                return NotFound();
            }

            return textData;
        }

        // POST: api/TextData
        [HttpPost]
        public async Task<ActionResult<TextData>> PostTextData(TextData textData)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            textData.CreatedAt = DateTime.UtcNow;
            textData.UpdatedAt = null;

            _context.TextData.Add(textData);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetTextData), new { id = textData.Id }, textData);
        }

        // PUT: api/TextData/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutTextData(int id, TextData textData)
        {
            if (id != textData.Id)
            {
                return BadRequest();
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            textData.UpdatedAt = DateTime.UtcNow;
            _context.Entry(textData).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TextDataExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/TextData/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTextData(int id)
        {
            var textData = await _context.TextData.FindAsync(id);
            if (textData == null)
            {
                return NotFound();
            }

            _context.TextData.Remove(textData);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool TextDataExists(int id)
        {
            return _context.TextData.Any(e => e.Id == id);
        }
    }
}
