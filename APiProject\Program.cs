using Microsoft.EntityFrameworkCore;
using APiProject.Data;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Add CORS policy for MVC project
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowMvcProject", policy =>
    {
        policy.WithOrigins("https://localhost:7001", "http://localhost:5001") // MVC project URLs
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

app.UseCors("AllowMvcProject");

app.UseAuthorization();

app.MapControllers();

app.Run();
