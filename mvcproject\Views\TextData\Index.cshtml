@model IEnumerable<mvcproject.Models.TextData>

@{
    ViewData["Title"] = "Text Data List";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>@ViewData["Title"]</h2>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Text
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (Model.Any())
            {
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>@Html.DisplayNameFor(model => model.Content)</th>
                                        <th>@Html.DisplayNameFor(model => model.CreatedAt)</th>
                                        <th>@Html.DisplayNameFor(model => model.UpdatedAt)</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td>
                                                @if (item.Content.Length > 100)
                                                {
                                                    @(item.Content.Substring(0, 100) + "...")
                                                }
                                                else
                                                {
                                                    @item.Content
                                                }
                                            </td>
                                            <td>@item.CreatedAt.ToString("MMM dd, yyyy HH:mm")</td>
                                            <td>
                                                @if (item.UpdatedAt.HasValue)
                                                {
                                                    @item.UpdatedAt.Value.ToString("MMM dd, yyyy HH:mm")
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Never</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">No Text Data Found</h5>
                        <p class="card-text">You haven't added any text data yet.</p>
                        <a asp-action="Create" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Your First Text
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>
