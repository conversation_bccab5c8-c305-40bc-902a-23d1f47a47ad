@model mvcproject.Models.TextData

@{
    ViewData["Title"] = "Delete Text Data";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">@ViewData["Title"]</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning!</strong> Are you sure you want to delete this text data? This action cannot be undone.
                    </div>

                    <dl class="row">
                        <dt class="col-sm-3">@Html.DisplayNameFor(model => model.Id)</dt>
                        <dd class="col-sm-9">@Html.DisplayFor(model => model.Id)</dd>

                        <dt class="col-sm-3">@Html.DisplayNameFor(model => model.Content)</dt>
                        <dd class="col-sm-9">
                            <div class="border p-3 bg-light rounded">
                                @Html.DisplayFor(model => model.Content)
                            </div>
                        </dd>

                        <dt class="col-sm-3">@Html.DisplayNameFor(model => model.CreatedAt)</dt>
                        <dd class="col-sm-9">@Model.CreatedAt.ToString("MMMM dd, yyyy 'at' HH:mm")</dd>

                        <dt class="col-sm-3">@Html.DisplayNameFor(model => model.UpdatedAt)</dt>
                        <dd class="col-sm-9">
                            @if (Model.UpdatedAt.HasValue)
                            {
                                @Model.UpdatedAt.Value.ToString("MMMM dd, yyyy 'at' HH:mm")
                            }
                            else
                            {
                                <span class="text-muted">Never updated</span>
                            }
                        </dd>
                    </dl>

                    <form asp-action="Delete">
                        <input type="hidden" asp-for="Id" />
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
