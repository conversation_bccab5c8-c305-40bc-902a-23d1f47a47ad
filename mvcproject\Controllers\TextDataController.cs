using Microsoft.AspNetCore.Mvc;
using mvcproject.Models;
using mvcproject.Services;

namespace mvcproject.Controllers
{
    public class TextDataController : Controller
    {
        private readonly ITextDataApiClient _apiClient;

        public TextDataController(ITextDataApiClient apiClient)
        {
            _apiClient = apiClient;
        }

        // GET: TextData
        public async Task<IActionResult> Index()
        {
            var textDataList = await _apiClient.GetAllAsync();
            return View(textDataList);
        }

        // GET: TextData/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var textData = await _apiClient.GetByIdAsync(id);
            if (textData == null)
            {
                return NotFound();
            }

            return View(textData);
        }

        // GET: TextData/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: TextData/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Content")] TextData textData)
        {
            if (ModelState.IsValid)
            {
                var createdTextData = await _apiClient.CreateAsync(textData);
                if (createdTextData != null)
                {
                    TempData["SuccessMessage"] = "Text data created successfully!";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = "Failed to create text data. Please try again.";
                }
            }
            return View(textData);
        }

        // GET: TextData/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var textData = await _apiClient.GetByIdAsync(id);
            if (textData == null)
            {
                return NotFound();
            }
            return View(textData);
        }

        // POST: TextData/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Content,CreatedAt,UpdatedAt")] TextData textData)
        {
            if (id != textData.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var success = await _apiClient.UpdateAsync(id, textData);
                if (success)
                {
                    TempData["SuccessMessage"] = "Text data updated successfully!";
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = "Failed to update text data. Please try again.";
                }
            }
            return View(textData);
        }

        // GET: TextData/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var textData = await _apiClient.GetByIdAsync(id);
            if (textData == null)
            {
                return NotFound();
            }

            return View(textData);
        }

        // POST: TextData/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var success = await _apiClient.DeleteAsync(id);
            if (success)
            {
                TempData["SuccessMessage"] = "Text data deleted successfully!";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to delete text data. Please try again.";
            }
            return RedirectToAction(nameof(Index));
        }
    }
}
