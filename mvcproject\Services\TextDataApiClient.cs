using mvcproject.Models;
using System.Text;
using System.Text.Json;

namespace mvcproject.Services
{
    public class TextDataApiClient : ITextDataApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;

        public TextDataApiClient(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<IEnumerable<TextData>> GetAllAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/textdata");
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                var textDataList = JsonSerializer.Deserialize<List<TextData>>(json, _jsonOptions);
                
                return textDataList ?? new List<TextData>();
            }
            catch (Exception)
            {
                return new List<TextData>();
            }
        }

        public async Task<TextData?> GetByIdAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/textdata/{id}");
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    return null;

                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<TextData>(json, _jsonOptions);
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<TextData?> CreateAsync(TextData textData)
        {
            try
            {
                var json = JsonSerializer.Serialize(textData, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("api/textdata", content);
                response.EnsureSuccessStatusCode();

                var responseJson = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<TextData>(responseJson, _jsonOptions);
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<bool> UpdateAsync(int id, TextData textData)
        {
            try
            {
                var json = JsonSerializer.Serialize(textData, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"api/textdata/{id}", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"api/textdata/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
