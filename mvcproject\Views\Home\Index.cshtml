﻿@{
    ViewData["Title"] = "Home Page";
}

<div class="text-center">
    <h1 class="display-4">Welcome</h1>
    <p class="lead">Simple Text Data Management Application</p>
    <p>This application demonstrates how to use MVC controllers to consume API endpoints using IHttpClientFactory with Typed Clients.</p>

    <div class="row mt-5">
        <div class="col-md-6 offset-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-database text-primary"></i> Text Data Management
                    </h5>
                    <p class="card-text">Store, view, edit, and delete text data through our API-powered interface.</p>
                    <a asp-controller="TextData" asp-action="Index" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> Get Started
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
